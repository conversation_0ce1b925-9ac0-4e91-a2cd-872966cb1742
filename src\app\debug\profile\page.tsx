"use client";

import { ProfileDebug } from "@/components/debug/ProfileDebug";
import { ProfileCard } from "@/components/profile/ProfileCard";
import { AuthGuard } from "@/components/auth/AuthGuard";

export default function ProfileDebugPage() {
  return (
    <AuthGuard>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">个人资料调试页面</h1>
        
        <div className="space-y-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">调试信息</h2>
            <ProfileDebug />
          </div>
          
          <div>
            <h2 className="text-xl font-semibold mb-4">实际组件显示</h2>
            <ProfileCard />
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}
