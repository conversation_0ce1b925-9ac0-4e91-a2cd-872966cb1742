import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import "@testing-library/jest-dom";

// Mock dependencies first
const mockPush = vi.fn();
const mockUseAuth = vi.fn();

vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

vi.mock("@/hooks/useAuth", () => ({
  useAuth: () => mockUseAuth(),
}));

// Import component after mocks
import PersonalMenu from "@/components/PersonalMenu";

describe("PersonalMenu", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("未登录状态", () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        user: null,
        logout: vi.fn(),
        isAdmin: vi.fn(() => false),
      });
    });

    it("应该显示用户图标按钮", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      expect(triggerButton).toBeInTheDocument();
    });

    it("点击按钮应该显示未登录状态", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      fireEvent.click(triggerButton);

      expect(screen.getByText("未登录状态")).toBeInTheDocument();
    });
  });

  describe("已登录状态", () => {
    const mockUser = {
      id: 1,
      username: "testuser",
      email: "<EMAIL>",
      role: { name: "user" },
    };

    const mockLogout = vi.fn();

    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        logout: mockLogout,
        isAdmin: vi.fn(() => false),
      });
    });

    it("应该显示用户信息", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      fireEvent.click(triggerButton);

      expect(screen.getByText("testuser")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    });

    it("应该显示所有菜单项", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      fireEvent.click(triggerButton);

      expect(screen.getByText("个人资料")).toBeInTheDocument();
      expect(screen.getByText("编辑资料")).toBeInTheDocument();
      expect(screen.getByText("积分历史")).toBeInTheDocument();
      expect(screen.getByText("我的求助")).toBeInTheDocument();
      expect(screen.getByText("退出登录")).toBeInTheDocument();
    });

    it("点击登出按钮应该调用logout函数", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      fireEvent.click(triggerButton);

      const logoutButton = screen.getByText("退出登录");
      fireEvent.click(logoutButton);
      expect(mockLogout).toHaveBeenCalled();
    });
  });

  describe("管理员状态", () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: {
          id: 1,
          username: "admin",
          email: "<EMAIL>",
          role: { name: "admin" },
        },
        logout: vi.fn(),
        isAdmin: () => true, // 直接返回true而不是vi.fn
      });
    });

    it("应该显示管理后台菜单项", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      fireEvent.click(triggerButton);

      expect(screen.getByText("管理后台")).toBeInTheDocument();
    });
  });

  describe("响应式设计", () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: {
          id: 1,
          username: "testuser",
          email: "<EMAIL>",
          role: { name: "user" },
        },
        logout: vi.fn(),
        isAdmin: () => false,
      });
    });

    it("应该在移动端正确显示", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      expect(triggerButton).toBeInTheDocument();

      // 检查按钮样式包含响应式类
      expect(triggerButton).toHaveClass("rounded-full");
    });

    it("下拉菜单应该有移动端适配样式", () => {
      render(<PersonalMenu />);

      const triggerButton = screen.getByRole("button", { name: "个人菜单" });
      fireEvent.click(triggerButton);

      const menu = screen.getByRole("menu");
      expect(menu).toHaveClass("absolute", "right-0");
    });
  });
});
